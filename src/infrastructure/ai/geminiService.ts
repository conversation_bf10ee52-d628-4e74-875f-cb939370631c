import { GeminiAPIResponse, FoodRecognitionResult } from '@/shared/types';

/**
 * Google Gemini AI服务
 */
export class GeminiService {
  private apiKey: string;
  private endpoint: string;
  private model: string;
  private timeout: number;

  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    // 使用官方Gemini API端点
    this.endpoint = import.meta.env.VITE_GEMINI_API_ENDPOINT || 'https://api-proxy.me/gemini';
    this.model = import.meta.env.VITE_GEMINI_MODEL || 'gemini-2.5-flash';
    this.timeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');

    if (!this.apiKey) {
      console.warn('Gemini API密钥未配置');
    }
  }

  /**
   * 识别食物图片
   */
  async recognizeFood(imageFile: File): Promise<FoodRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      // 将图片转换为base64
      const base64Image = await this.fileToBase64(imageFile);

      // 发送请求
      const response = await this.sendRequest(base64Image);
      
      // 解析响应
      return this.parseResponse(response);
    } catch (error) {
      console.error('食物识别失败:', error);
      throw new Error(error instanceof Error ? error.message : '食物识别失败');
    }
  }

  /**
   * 构建食物识别提示词
   */
  private buildFoodRecognitionPrompt(): string {
    return `
请分析这张图片中的食物，并以JSON格式返回详细信息。

要求：
1. 识别图片中所有可见的食物
2. 估算每种食物的重量（克）
3. 计算每种食物的卡路里
4. 提供识别置信度（0-1）
5. 如果不确定，提供备选识别结果

返回格式：
{
  "foods": [
    {
      "name": "食物名称",
      "calories": 卡路里数值,
      "weight": 重量(克),
      "confidence": 置信度(0-1),
      "alternatives": ["备选名称1", "备选名称2"]
    }
  ]
}

注意事项：
- 如果图片中没有食物，返回空的foods数组
- 卡路里计算基于标准营养数据
- 重量估算考虑食物的密度和体积
- 置信度反映识别的准确性
- 只返回JSON，不要其他文字

请分析图片：
    `.trim();
  }

  /**
   * 发送API请求
   */
  private async sendRequest(base64Image: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 使用正确的Gemini API端点和认证方式
      const response = await fetch(`${this.endpoint}/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: this.buildFoodRecognitionPrompt()
                },
                {
                  inline_data: {
                    mime_type: 'image/jpeg',
                    data: base64Image.split(',')[1] // 移除data:image/jpeg;base64,前缀
                  }
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.1,
            topK: 1,
            topP: 1,
            maxOutputTokens: 2048
          }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      
      throw error;
    }
  }

  /**
   * 解析API响应
   */
  private parseResponse(response: any): FoodRecognitionResult {
    try {
      // 提取生成的文本
      const candidates = response.candidates;
      if (!candidates || candidates.length === 0) {
        throw new Error('API响应格式错误：没有候选结果');
      }

      const content = candidates[0].content;
      if (!content || !content.parts || content.parts.length === 0) {
        throw new Error('API响应格式错误：没有内容');
      }

      const text = content.parts[0].text;
      if (!text) {
        throw new Error('API响应格式错误：没有文本内容');
      }

      // 尝试解析JSON
      let jsonData: any;
      try {
        // 清理文本，移除可能的markdown格式
        const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        jsonData = JSON.parse(cleanText);
      } catch (parseError) {
        console.error('JSON解析失败:', text);
        throw new Error('AI响应格式错误，无法解析结果');
      }

      // 验证数据格式
      if (!jsonData.foods || !Array.isArray(jsonData.foods)) {
        throw new Error('AI响应格式错误：foods字段缺失或格式错误');
      }

      // 验证每个食物条目
      const validatedFoods = jsonData.foods.map((food: any, index: number) => {
        if (!food.name || typeof food.name !== 'string') {
          throw new Error(`食物${index + 1}：名称缺失或格式错误`);
        }
        
        if (typeof food.calories !== 'number' || food.calories < 0) {
          throw new Error(`食物${index + 1}：卡路里数值错误`);
        }
        
        if (typeof food.weight !== 'number' || food.weight <= 0) {
          throw new Error(`食物${index + 1}：重量数值错误`);
        }
        
        if (typeof food.confidence !== 'number' || food.confidence < 0 || food.confidence > 1) {
          throw new Error(`食物${index + 1}：置信度数值错误`);
        }

        return {
          name: food.name.trim(),
          calories: Math.round(food.calories),
          weight: Math.round(food.weight),
          confidence: Number(food.confidence.toFixed(2)),
          alternatives: Array.isArray(food.alternatives) ? food.alternatives : []
        };
      });

      return {
        foods: validatedFoods,
        rawResponse: response
      };
    } catch (error) {
      console.error('解析API响应失败:', error);
      throw new Error(error instanceof Error ? error.message : '解析AI响应失败');
    }
  }

  /**
   * 将文件转换为base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('文件读取失败'));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsDataURL(file);
    });
  }

  /**
   * 检查API配置
   */
  isConfigured(): boolean {
    return !!this.apiKey && !!this.endpoint;
  }

  /**
   * 获取配置信息
   */
  getConfig() {
    return {
      hasApiKey: !!this.apiKey,
      endpoint: this.endpoint,
      model: this.model,
      timeout: this.timeout
    };
  }
}

// 创建全局实例
export const geminiService = new GeminiService();
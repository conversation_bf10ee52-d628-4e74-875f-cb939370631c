import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import NavigationButton from './NavigationButton';
import { RecordIcon, CalendarIcon, ProfileIcon, DashboardIcon } from './NavigationIcons';

interface BottomNavigationProps {
  onRecordFood: () => void;
  onViewProfile: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  onRecordFood,
  onViewProfile
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // 导航配置 - 2025年最佳实践：配置驱动
  const navigationItems = [
    {
      icon: <DashboardIcon />,
      label: '首页',
      path: '/dashboard',
      onClick: () => navigate('/dashboard'),
      ariaLabel: '首页'
    },
    {
      icon: <RecordIcon />,
      label: '记录',
      path: '/record',
      onClick: () => navigate('/food-record'),
      ariaLabel: '开始记录食物'
    },
    {
      icon: <CalendarIcon />,
      label: '日历',
      path: '/calendar',
      onClick: () => navigate('/calendar'),
      ariaLabel: '查看日历'
    },
    {
      icon: <ProfileIcon />,
      label: '我的',
      path: '/profile',
      onClick: () => navigate('/profile'),
      ariaLabel: '我的'
    }
  ];

  return (
    <div
      className="dock fixed bottom-0 left-0 right-0 bg-base-100"
      style={{
        // 2025年移动端标准：安全区域适配
        position: 'fixed',
        zIndex: 1000,
        // 避免层叠上下文问题
        transform: 'none'
      }}
    >
      {navigationItems.map((item) => (
        <NavigationButton
          key={item.path}
          icon={item.icon}
          label={item.label}
          isActive={isActive(item.path)}
          onClick={item.onClick}
          ariaLabel={item.ariaLabel}
        />
      ))}
    </div>
  );
};

export default BottomNavigation;
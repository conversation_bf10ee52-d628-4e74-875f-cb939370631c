import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ImagePicker } from '@/shared/components';
import { geminiService } from '@/infrastructure/ai/geminiService';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { useUserStore } from '@/domains/user/stores/userStore';
import { FoodRecognitionResult, FoodRecord } from '@/shared/types';
import BottomNavigation from '@/shared/components/navigation/BottomNavigation';

const FoodRecordPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useUserStore();
  const { addDetailedFoodRecord, getDailyFoodRecords, getFoodRecordsByMeal } = useNutritionStore();

  // 页面状态
  const [currentDate, setCurrentDate] = useState(new Date());
  const [activeView, setActiveView] = useState<'list' | 'add-text' | 'add-image'>('list');

  // 添加功能状态
  const [textInput, setTextInput] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 编辑状态
  const [editingRecord, setEditingRecord] = useState<FoodRecord | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // 获取当日食物记录
  const dailyRecords = getDailyFoodRecords(currentDate);

  // Anime.js v4兼容动画系统
  useEffect(() => {
    import('@/utils/animations').then(({ createPageLoadAnimation }) => {
      const cardElements = [
        '.timeline-card',
        '.food-card',
        '.action-button',
        '.add-button'
      ];
      createPageLoadAnimation(cardElements);
    });
  }, [currentDate]);

  // 获取当前餐次
  const getCurrentMealType = (): 'breakfast' | 'lunch' | 'dinner' => {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 10) return 'breakfast';
    if (hour >= 11 && hour < 14) return 'lunch';
    return 'dinner';
  };

  // 处理文字分析
  const handleTextAnalysis = async () => {
    if (!textInput.trim()) return;

    setIsProcessing(true);
    setError(null);

    try {
      // 使用Gemini API分析文字
      const result = await geminiService.analyzeTextFood(textInput);

      if (!result.foods || result.foods.length === 0) {
        setError('未能从文字中识别到食物信息，请尝试更详细的描述');
        return;
      }

      // 转换为食物记录并保存
      for (const food of result.foods) {
        const foodRecord: Omit<FoodRecord, 'id' | 'createdAt' | 'updatedAt'> = {
          name: food.name,
          weight: food.weight,
          calories: food.calories,
          mealType: getCurrentMealType(),
          recordedAt: new Date(),
          nutrition: {
            protein: food.nutrition?.protein || 0,
            fat: food.nutrition?.fat || 0,
            carbs: food.nutrition?.carbs || 0,
            fiber: food.nutrition?.fiber || 0,
            sugar: food.nutrition?.sugar || 0
          },
          aiRecognition: {
            confidence: food.confidence,
            method: 'text',
            originalInput: textInput
          },
          isEdited: false
        };

        addDetailedFoodRecord(currentDate, foodRecord);
      }

      // 重置状态
      setTextInput('');
      setActiveView('list');
    } catch (error) {
      console.error('文字分析失败:', error);
      setError('文字分析失败，请检查网络连接后重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理图片选择
  const handleImageSelect = async (file: File, thumbnail?: string) => {
    setError(null);

    try {
      setSelectedImage(file);
      setImagePreview(thumbnail || URL.createObjectURL(file));
    } catch (err) {
      setError('图片处理失败，请重试');
      console.error('图片处理错误:', err);
    }
  };

  // 处理图片分析
  const handleImageAnalysis = async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setError(null);

    try {
      const result = await geminiService.recognizeFood(selectedImage);

      if (!result.foods || result.foods.length === 0) {
        setError('未能识别到食物，请确保图片清晰并包含食物');
        return;
      }

      // 转换为食物记录并保存
      for (const food of result.foods) {
        const foodRecord: Omit<FoodRecord, 'id' | 'createdAt' | 'updatedAt'> = {
          name: food.name,
          weight: food.weight,
          calories: food.calories,
          mealType: getCurrentMealType(),
          recordedAt: new Date(),
          nutrition: {
            protein: food.nutrition?.protein || 0,
            fat: food.nutrition?.fat || 0,
            carbs: food.nutrition?.carbs || 0,
            fiber: food.nutrition?.fiber || 0,
            sugar: food.nutrition?.sugar || 0
          },
          aiRecognition: {
            confidence: food.confidence,
            method: 'image',
            originalInput: selectedImage.name
          },
          isEdited: false
        };

        addDetailedFoodRecord(currentDate, foodRecord);
      }

      // 重置状态
      setSelectedImage(null);
      setImagePreview('');
      setActiveView('list');
    } catch (error) {
      console.error('图片分析失败:', error);
      setError('图片分析失败，请检查网络连接后重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // Anime.js v4按钮点击动画
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    import('@/utils/animations').then(({ createButtonAnimation }) => {
      createButtonAnimation(e.currentTarget as HTMLElement);
    });
  };

  const handleCardHover = (e: React.MouseEvent<HTMLDivElement>) => {
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-hover-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-hover-target', true);
    });
  };

  const handleCardLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-leave-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-leave-target', false);
    });
    setTimeout(() => {
      element.classList.remove('card-hover-target', 'card-leave-target');
    }, 350);
  };

  // AI识别食物
  const analyzeFood = async () => {
    if (!selectedImage) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      const result = await geminiService.recognizeFood(selectedImage);
      setRecognitionResult(result);

      // 检查识别结果
      if (!result.foods || result.foods.length === 0) {
        setError('未能识别到食物，请确保图片清晰并包含食物');
        return;
      }

      // 转换为可编辑的食物项目
      const items: FoodItem[] = result.foods.map((food, index) => ({
        id: `food_${Date.now()}_${index}`,
        name: food.name,
        weight: food.weight,
        calories: food.calories,
        confidence: food.confidence,
        mealType: getCurrentMealType()
      }));

      setFoodItems(items);
      setCurrentStep('edit');
    } catch (error) {
      console.error('食物识别失败:', error);
      const errorMessage = error instanceof Error ? error.message : '食物识别失败，请检查网络连接后重试';
      setError(errorMessage);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 更新食物项目
  const updateFoodItem = (id: string, updates: Partial<FoodItem>) => {
    setFoodItems(prev => prev.map(item => 
      item.id === id ? { ...item, ...updates } : item
    ));
  };

  // 删除食物项目
  const deleteFoodItem = (id: string) => {
    setFoodItems(prev => prev.filter(item => item.id !== id));
  };

  // 保存记录
  const saveRecord = async () => {
    if (foodItems.length === 0) return;

    setIsSaving(true);
    setError(null);

    try {
      const today = new Date();
      const calorieLimit = profile?.dailyCalorieLimit || 2000;

      // 按餐次添加食物记录
      foodItems.forEach(item => {
        addFoodRecord(today, item.mealType, item.calories, calorieLimit);
      });

      // 短暂延迟给用户反馈
      await new Promise(resolve => setTimeout(resolve, 800));

      // 返回Dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('保存失败:', error);
      setError('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 底部导航处理
  const handleRecordFood = () => {
    // 已经在食物记录页面
  };

  const handleViewProfile = () => {
    navigate('/profile');
  };

  return (
    <div className="relative">
      {/* 主要内容容器 */}
      <div
        className="min-h-screen bg-base-200"
        style={{
          transform: 'none',
          isolation: 'auto',
          position: 'relative',
          zIndex: 'auto'
        }}
      >
        <div className="w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="space-y-4 sm:space-y-6">
            {/* 页面标题和日期选择器 */}
            <div className="text-center mb-6">
              <h1 className="text-2xl sm:text-3xl font-bold text-base-content mb-4">
                食物记录
              </h1>

              {/* 日期选择器 */}
              <div className="flex items-center justify-center gap-4 mb-4">
                <button
                  className="btn btn-circle btn-sm"
                  onClick={() => {
                    const prevDay = new Date(currentDate);
                    prevDay.setDate(prevDay.getDate() - 1);
                    setCurrentDate(prevDay);
                  }}
                >
                  ‹
                </button>
                <div className="text-lg font-medium">
                  {currentDate.toLocaleDateString('zh-CN', {
                    month: 'long',
                    day: 'numeric',
                    weekday: 'short'
                  })}
                </div>
                <button
                  className="btn btn-circle btn-sm"
                  onClick={() => {
                    const nextDay = new Date(currentDate);
                    nextDay.setDate(nextDay.getDate() + 1);
                    setCurrentDate(nextDay);
                  }}
                  disabled={currentDate >= new Date()}
                >
                  ›
                </button>
              </div>
            </div>

            {/* 每日统计卡片 */}
            <div className="timeline-card card bg-base-100 shadow-lg">
              <div className="card-body">
                <div className="stats stats-horizontal">
                  <div className="stat">
                    <div className="stat-title">总卡路里</div>
                    <div className="stat-value text-primary">
                      {dailyRecords?.records.reduce((sum, r) => sum + r.calories, 0) || 0}
                    </div>
                    <div className="stat-desc">/ 2000 kcal</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title">食物数量</div>
                    <div className="stat-value text-secondary">
                      {dailyRecords?.records.length || 0}
                    </div>
                    <div className="stat-desc">项记录</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 添加按钮组 */}
            <div className="flex gap-2 justify-center mb-6">
              <button
                className="add-button btn btn-primary btn-sm"
                onClick={() => setActiveView('add-text')}
              >
                📝 文字识别
              </button>
              <button
                className="add-button btn btn-secondary btn-sm"
                onClick={() => setActiveView('add-image')}
              >
                📷 图片识别
              </button>
            </div>

            {/* 时间轴视图 */}
            {activeView === 'list' && (
              <div className="space-y-6">
                {/* 早餐 */}
                <div className="timeline-card">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-2xl">🌅</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">早餐</h3>
                      <p className="text-sm text-base-content/70">6:00 - 10:00</p>
                    </div>
                    <div className="ml-auto">
                      <div className="badge badge-primary">
                        {getFoodRecordsByMeal(currentDate, 'breakfast').reduce((sum, r) => sum + r.calories, 0)} kcal
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {getFoodRecordsByMeal(currentDate, 'breakfast').map((record) => (
                      <div key={record.id} className="food-card card card-compact bg-base-200">
                        <div className="card-body">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{record.name}</h4>
                              <p className="text-sm text-base-content/70">
                                {record.weight}g • {record.calories} kcal
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <button
                                className="btn btn-ghost btn-xs"
                                onClick={() => {
                                  setEditingRecord(record);
                                  setShowEditModal(true);
                                }}
                              >
                                编辑
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {getFoodRecordsByMeal(currentDate, 'breakfast').length === 0 && (
                      <div className="text-center py-8 text-base-content/50">
                        还没有早餐记录
                      </div>
                    )}
                  </div>
                </div>

                {/* 午餐 */}
                <div className="timeline-card">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                      <span className="text-2xl">☀️</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">午餐</h3>
                      <p className="text-sm text-base-content/70">11:00 - 14:00</p>
                    </div>
                    <div className="ml-auto">
                      <div className="badge badge-secondary">
                        {getFoodRecordsByMeal(currentDate, 'lunch').reduce((sum, r) => sum + r.calories, 0)} kcal
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {getFoodRecordsByMeal(currentDate, 'lunch').map((record) => (
                      <div key={record.id} className="food-card card card-compact bg-base-200">
                        <div className="card-body">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{record.name}</h4>
                              <p className="text-sm text-base-content/70">
                                {record.weight}g • {record.calories} kcal
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <button
                                className="btn btn-ghost btn-xs"
                                onClick={() => {
                                  setEditingRecord(record);
                                  setShowEditModal(true);
                                }}
                              >
                                编辑
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {getFoodRecordsByMeal(currentDate, 'lunch').length === 0 && (
                      <div className="text-center py-8 text-base-content/50">
                        还没有午餐记录
                      </div>
                    )}
                  </div>
                </div>

                {/* 晚餐 */}
                <div className="timeline-card">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-2xl">🌙</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">晚餐</h3>
                      <p className="text-sm text-base-content/70">17:00 - 21:00</p>
                    </div>
                    <div className="ml-auto">
                      <div className="badge badge-accent">
                        {getFoodRecordsByMeal(currentDate, 'dinner').reduce((sum, r) => sum + r.calories, 0)} kcal
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {getFoodRecordsByMeal(currentDate, 'dinner').map((record) => (
                      <div key={record.id} className="food-card card card-compact bg-base-200">
                        <div className="card-body">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{record.name}</h4>
                              <p className="text-sm text-base-content/70">
                                {record.weight}g • {record.calories} kcal
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <button
                                className="btn btn-ghost btn-xs"
                                onClick={() => {
                                  setEditingRecord(record);
                                  setShowEditModal(true);
                                }}
                              >
                                编辑
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {getFoodRecordsByMeal(currentDate, 'dinner').length === 0 && (
                      <div className="text-center py-8 text-base-content/50">
                        还没有晚餐记录
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 文字识别界面 */}
            {activeView === 'add-text' && (
              <div className="timeline-card card bg-base-100 shadow-lg">
                <div className="card-body">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="card-title">📝 文字识别</h3>
                    <button
                      className="btn btn-ghost btn-sm"
                      onClick={() => setActiveView('list')}
                    >
                      ✕
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="label">
                        <span className="label-text">描述你吃的食物</span>
                      </label>
                      <textarea
                        className="textarea textarea-bordered w-full h-24"
                        placeholder="例如：我今天早餐吃了一个苹果和一杯牛奶..."
                        value={textInput}
                        onChange={(e) => setTextInput(e.target.value)}
                      />
                    </div>

                    {error && (
                      <div className="alert alert-error">
                        <span>{error}</span>
                      </div>
                    )}

                    <div className="card-actions justify-end">
                      <button
                        className="btn btn-primary"
                        onClick={handleTextAnalysis}
                        disabled={!textInput.trim() || isProcessing}
                      >
                        {isProcessing ? (
                          <>
                            <span className="loading loading-spinner loading-sm"></span>
                            AI分析中...
                          </>
                        ) : (
                          '开始AI分析'
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 图片识别界面 */}
            {activeView === 'add-image' && (
              <div className="timeline-card card bg-base-100 shadow-lg">
                <div className="card-body">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="card-title">📷 图片识别</h3>
                    <button
                      className="btn btn-ghost btn-sm"
                      onClick={() => setActiveView('list')}
                    >
                      ✕
                    </button>
                  </div>

                  <div className="space-y-4">

                    {/* 图片预览 */}
                    {imagePreview && (
                      <div className="text-center">
                        <img
                          src={imagePreview}
                          alt="选择的食物图片"
                          className="w-full max-w-sm mx-auto rounded-lg shadow-lg mb-4"
                        />
                        <p className="text-sm text-base-content/70 mb-4">
                          图片已准备就绪，点击下方按钮开始AI识别
                        </p>
                      </div>
                    )}

                    {/* 处理状态提示 */}
                    {isProcessing && (
                      <div className="text-center mb-4">
                        <div className="loading loading-spinner loading-lg text-primary mb-2"></div>
                        <p className="text-primary font-medium">AI正在分析图片中的食物...</p>
                      </div>
                    )}

                    {/* 错误提示 */}
                    {error && (
                      <div className="alert alert-error mb-4">
                        <span className="text-sm">{error}</span>
                        <button
                          className="btn btn-ghost btn-sm"
                          onClick={() => setError(null)}
                        >
                          ✕
                        </button>
                      </div>
                    )}

                    {!imagePreview && (
                      <ImagePicker
                        onImageSelect={handleImageSelect}
                        maxSize={parseInt(import.meta.env.VITE_MAX_IMAGE_SIZE || '2097152')}
                        accept="image/*"
                        className="w-full"
                        disabled={isProcessing}
                      />
                    )}

                    {imagePreview && (
                      <div className="space-y-3">
                        <button
                          className="btn btn-primary w-full"
                          onClick={handleImageAnalysis}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <>
                              <span className="loading loading-spinner loading-sm"></span>
                              AI识别中...
                            </>
                          ) : (
                            '开始AI识别'
                          )}
                        </button>

                        <button
                          className="btn btn-ghost w-full"
                          onClick={() => {
                            setImagePreview('');
                            setSelectedImage(null);
                            setError(null);
                          }}
                          disabled={isProcessing}
                        >
                          重新选择图片
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 编辑模态框 */}
            {showEditModal && editingRecord && (
              <div
                className="food-record-card relative overflow-hidden bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-orange-200/50 shadow-xl"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-xl">🤖</span>
                    </div>
                    <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                      AI智能识别
                    </h3>
                  </div>

                  {/* 图片预览 */}
                  {imagePreview && (
                    <div className="mb-6">
                      <img
                        src={imagePreview}
                        alt="选择的食物图片"
                        className="w-full max-w-sm mx-auto rounded-xl shadow-lg"
                      />
                      <p className="text-center text-sm text-slate-600 mt-2">
                        图片已准备就绪，点击下方按钮开始AI识别
                      </p>
                    </div>
                  )}

                  {/* 错误提示 */}
                  {error && (
                    <div className="alert alert-error mb-4">
                      <span className="text-sm">{error}</span>
                      <div className="flex gap-2">
                        <button
                          className="btn btn-ghost btn-sm"
                          onClick={() => setError(null)}
                        >
                          ✕
                        </button>
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={(e) => { handleButtonClick(e); analyzeFood(); }}
                          disabled={isAnalyzing}
                        >
                          重试
                        </button>
                      </div>
                    </div>
                  )}

                  {/* AI识别状态提示 */}
                  {isAnalyzing && (
                    <div className="text-center mb-6">
                      <div className="loading loading-spinner loading-lg text-orange-500 mb-3"></div>
                      <p className="text-orange-600 font-medium mb-2">AI正在分析图片中的食物...</p>
                      <p className="text-sm text-slate-500">这可能需要几秒钟时间</p>
                    </div>
                  )}

                  {/* 分析按钮 */}
                  <div className="space-y-4">
                    <button
                      className="action-button btn btn-primary btn-lg w-full h-14 shadow-lg"
                      onClick={(e) => { handleButtonClick(e); analyzeFood(); }}
                      disabled={isAnalyzing}
                    >
                      {isAnalyzing ? (
                        <>
                          <span className="loading loading-spinner loading-md mr-2"></span>
                          AI识别中...
                        </>
                      ) : (
                        <>
                          <span className="text-xl mr-2">🔍</span>
                          开始AI识别
                        </>
                      )}
                    </button>

                    <button
                      className="action-button btn btn-ghost btn-lg w-full h-12 shadow-lg"
                      onClick={(e) => {
                        handleButtonClick(e);
                        setCurrentStep('upload');
                        setError(null);
                        setSelectedImage(null);
                        setImagePreview('');
                      }}
                      disabled={isAnalyzing}
                    >
                      <span className="text-lg mr-2">↩️</span>
                      重新选择图片
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* 步骤3：编辑食物信息 */}
            {currentStep === 'edit' && (
              <div className="space-y-4">
                {/* 识别结果标题 */}
                <div
                  className="food-record-card relative overflow-hidden bg-gradient-to-br from-violet-50 via-purple-50 to-indigo-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-violet-200/50 shadow-xl"
                  onMouseEnter={handleCardHover}
                  onMouseLeave={handleCardLeave}
                >
                  <div className="relative z-10">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                        <span className="text-xl">✏️</span>
                      </div>
                      <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                        编辑食物信息
                      </h3>
                    </div>
                    <div className="space-y-2">
                      <p className="text-slate-600">请确认或调整AI识别的食物信息</p>
                      {foodItems.length > 0 && (
                        <div className="alert alert-success">
                          <span className="text-sm">
                            ✅ 成功识别到 {foodItems.length} 种食物，总计约 {Math.round(foodItems.reduce((sum, item) => sum + item.calories, 0))} 卡路里
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 食物项目列表 */}
                {foodItems.map((item, index) => (
                  <div
                    key={item.id}
                    className="food-item-card relative overflow-hidden bg-white/90 backdrop-blur-sm rounded-2xl p-4 border border-white/50 shadow-lg"
                    onMouseEnter={handleCardHover}
                    onMouseLeave={handleCardLeave}
                  >
                    <div className="space-y-4">
                      {/* 食物名称 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          食物名称
                        </label>
                        <input
                          type="text"
                          value={item.name}
                          onChange={(e) => updateFoodItem(item.id, { name: e.target.value })}
                          className="input input-bordered w-full h-12"
                          placeholder="请输入食物名称"
                        />
                      </div>

                      {/* 重量调整 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          重量 (克)
                        </label>
                        <div className="flex items-center gap-2">
                          <button
                            className="btn btn-circle btn-sm"
                            onClick={() => updateFoodItem(item.id, {
                              weight: Math.max(1, item.weight - 10)
                            })}
                          >
                            -
                          </button>
                          <input
                            type="number"
                            value={item.weight}
                            onChange={(e) => updateFoodItem(item.id, {
                              weight: Math.max(1, Math.min(2000, parseInt(e.target.value) || 0))
                            })}
                            className="input input-bordered flex-1 h-12 text-center"
                            min="1"
                            max="2000"
                          />
                          <button
                            className="btn btn-circle btn-sm"
                            onClick={() => updateFoodItem(item.id, {
                              weight: Math.min(2000, item.weight + 10)
                            })}
                          >
                            +
                          </button>
                        </div>
                      </div>

                      {/* 卡路里调整 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          卡路里 (kcal)
                        </label>
                        <div className="flex items-center gap-2">
                          <button
                            className="btn btn-circle btn-sm"
                            onClick={() => updateFoodItem(item.id, {
                              calories: Math.max(1, item.calories - 10)
                            })}
                          >
                            -
                          </button>
                          <input
                            type="number"
                            value={item.calories}
                            onChange={(e) => updateFoodItem(item.id, {
                              calories: Math.max(1, parseInt(e.target.value) || 0)
                            })}
                            className="input input-bordered flex-1 h-12 text-center"
                            min="1"
                          />
                          <button
                            className="btn btn-circle btn-sm"
                            onClick={() => updateFoodItem(item.id, {
                              calories: item.calories + 10
                            })}
                          >
                            +
                          </button>
                        </div>
                      </div>

                      {/* 餐次选择 */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          餐次分类
                        </label>
                        <select
                          value={item.mealType}
                          onChange={(e) => updateFoodItem(item.id, {
                            mealType: e.target.value as 'breakfast' | 'lunch' | 'dinner'
                          })}
                          className="select select-bordered w-full h-12"
                        >
                          <option value="breakfast">🌅 早餐</option>
                          <option value="lunch">☀️ 午餐</option>
                          <option value="dinner">🌙 晚餐</option>
                        </select>
                      </div>

                      {/* 置信度显示 */}
                      <div className="flex items-center justify-between text-sm text-slate-600">
                        <span>AI识别置信度: {Math.round(item.confidence * 100)}%</span>
                        <button
                          className="btn btn-ghost btn-sm text-red-500"
                          onClick={() => deleteFoodItem(item.id)}
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                {/* 保存状态提示 */}
                {isSaving && (
                  <div className="text-center mb-4">
                    <div className="loading loading-spinner loading-lg text-primary mb-2"></div>
                    <p className="text-primary font-medium">正在保存食物记录...</p>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="space-y-3">
                  <button
                    className="action-button btn btn-primary btn-lg w-full h-14 shadow-lg"
                    onClick={(e) => { handleButtonClick(e); saveRecord(); }}
                    disabled={foodItems.length === 0 || isSaving}
                  >
                    {isSaving ? (
                      <>
                        <span className="loading loading-spinner loading-md mr-2"></span>
                        保存中...
                      </>
                    ) : (
                      <>
                        <span className="text-xl mr-2">💾</span>
                        保存食物记录
                      </>
                    )}
                  </button>

                  <div className="grid grid-cols-2 gap-3">
                    <button
                      className="action-button btn btn-ghost btn-lg h-12 shadow-lg"
                      onClick={(e) => {
                        handleButtonClick(e);
                        setCurrentStep('analyze');
                        setError(null);
                      }}
                      disabled={isSaving}
                    >
                      <span className="text-lg mr-2">↩️</span>
                      重新识别
                    </button>
                    <button
                      className="action-button btn btn-ghost btn-lg h-12 shadow-lg"
                      onClick={(e) => { handleButtonClick(e); navigate('/dashboard'); }}
                      disabled={isSaving}
                    >
                      <span className="text-lg mr-2">❌</span>
                      取消
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* 底部留白 */}
            <div className="h-24 pb-safe"></div>
          </div>
        </div>
      </div>

      {/* 底部导航栏 */}
      <BottomNavigation
        onRecordFood={handleRecordFood}
        onViewProfile={handleViewProfile}
      />
    </div>
  );
};

export default FoodRecordPage;

import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { NutritionCard, NutritionAdvice } from '@/domains/nutrition';
import { formatCalories, formatWeight, formatDate } from '@/shared/utils';
import BottomNavigation from '@/shared/components/navigation/BottomNavigation';


const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile, clearProfile } = useUserStore();
  const { getDailySummary, updateDailySummary } = useNutritionStore();

  // 移除Anime.js相关的refs



  const today = new Date();
  const todaySummary = getDailySummary(today);

  // Anime.js v4兼容动画系统 - 确保不影响底部导航栏
  useEffect(() => {
    // 导入Anime.js动画函数
    import('@/utils/animations').then(({ createPageLoadAnimation, createCardHoverAnimation }) => {
      // 页面加载动画 - 使用opacity避免transform影响fixed定位
      const cardElements = [
        '.dashboard-card',
        '.action-button',
        '.hero-section',
        '.nutrition-card',
        '.profile-card'
      ];

      // 创建页面加载动画
      createPageLoadAnimation(cardElements);

      // 添加数字递增动画
      setTimeout(() => {
        const { createCountUpAnimation, createProgressAnimation } = require('@/utils/animations');

        // 卡路里数字动画
        const calorieElements = document.querySelectorAll('.calorie-number');
        calorieElements.forEach((element: Element) => {
          const htmlElement = element as HTMLElement;
          const targetValue = parseInt(htmlElement.textContent || '0');
          if (targetValue > 0) {
            createCountUpAnimation(htmlElement, targetValue, 1200);
          }
        });

        // 进度条动画
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach((element: Element) => {
          const htmlElement = element as HTMLElement;
          const targetWidth = parseFloat(htmlElement.style.width || '0');
          if (targetWidth > 0) {
            createProgressAnimation(htmlElement, targetWidth, 1000);
          }
        });

        // 验证底部导航栏位置未受影响
        const dock = document.querySelector('.dock') as HTMLElement;
        if (dock) {
          const computedStyle = window.getComputedStyle(dock);
          console.log('🔍 Dock验证 - Anime.js动画后:', {
            position: computedStyle.position,
            bottom: computedStyle.bottom,
            zIndex: computedStyle.zIndex,
            transform: computedStyle.transform
          });
        }
      }, 500);
    });
  }, []);



  // 初始化今日营养数据
  useEffect(() => {
    if (profile && !todaySummary) {
      // 创建默认的每日汇总
      updateDailySummary(today, {
        totalCalories: 0,
        calorieLimit: profile.dailyCalorieLimit,
        remainingCalories: profile.dailyCalorieLimit,
        mealBreakdown: {
          breakfast: {
            mealType: 'breakfast',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.breakfast),
            foodCount: 0,
            percentage: 0
          },
          lunch: {
            mealType: 'lunch',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.lunch),
            foodCount: 0,
            percentage: 0
          },
          dinner: {
            mealType: 'dinner',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.dinner),
            foodCount: 0,
            percentage: 0
          },
          snack: {
            mealType: 'snack',
            calories: 0,
            calorieLimit: 0,
            foodCount: 0,
            percentage: 0
          }
        },
        nutrition: {
          protein: 0,
          fat: 0,
          carbs: 0,
          fiber: 0,
          sugar: 0
        },
        status: 'under',
        percentage: 0
      });
    }
  }, [profile, todaySummary, updateDailySummary, today]);

  // Anime.js v4按钮点击动画 - 兼容底部导航栏
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    import('@/utils/animations').then(({ createButtonAnimation }) => {
      createButtonAnimation(e.currentTarget as HTMLElement);
    });
  };

  const handleCardHover = (e: React.MouseEvent<HTMLDivElement>) => {
    // Anime.js v4卡片hover动画 - 兼容底部导航栏
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-hover-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-hover-target', true);
    });
  };

  const handleCardLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    // Anime.js v4卡片leave动画
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-leave-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-leave-target', false);
    });
    setTimeout(() => {
      element.classList.remove('card-hover-target', 'card-leave-target');
    }, 350);
  };

  // 底部导航栏处理函数
  const handleRecordFood = () => {
    alert('食物记录功能即将推出');
  };

  const handleViewProfile = () => {
    // 滚动到个人档案区域
    const profileSection = document.querySelector('[data-section="profile"]');
    if (profileSection) {
      profileSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (!profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
            <span className="text-2xl">👤</span>
          </div>
          <h2 className="text-xl font-bold text-slate-800 mb-2">
            未找到用户档案
          </h2>
          <p className="text-slate-600 mb-4">
            请先完成个人档案设置
          </p>
          <button
            className="btn btn-primary btn-lg"
            onClick={() => navigate('/setup')}
          >
            立即设置
          </button>
        </div>
      </div>
    );
  }

  const weightLoss = profile.weight - profile.targetWeight;
  const weeklyLoss = (weightLoss / profile.targetDays) * 7;

  // 模拟添加一些测试数据
  const addTestData = () => {
    if (todaySummary) {
      const testCalories = 650;
      updateDailySummary(today, {
        totalCalories: todaySummary.totalCalories + testCalories,
        mealBreakdown: {
          ...todaySummary.mealBreakdown,
          breakfast: {
            ...todaySummary.mealBreakdown.breakfast,
            calories: todaySummary.mealBreakdown.breakfast.calories + testCalories,
            foodCount: todaySummary.mealBreakdown.breakfast.foodCount + 1
          }
        },
        nutrition: {
          protein: todaySummary.nutrition.protein + 25,
          fat: todaySummary.nutrition.fat + 15,
          carbs: todaySummary.nutrition.carbs + 45,
          fiber: todaySummary.nutrition.fiber + 5,
          sugar: todaySummary.nutrition.sugar + 10
        }
      });
    }
  };

  return (
    <div className="relative">
      {/* 主要内容容器 - 完全避免transform，确保不影响fixed定位 */}
      <div
        className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50"
        style={{
          // 强制确保没有任何transform属性
          transform: 'none',
          // 避免创建层叠上下文
          isolation: 'auto',
          // 确保不影响fixed定位的子元素
          position: 'relative',
          zIndex: 'auto'
        }}
      >
        {/* 移动端优先的容器设计 */}
        <div className="w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* 今日营养概览 - 移动端优先设计 */}
          {todaySummary && (
            <div className="space-y-4 sm:space-y-6">
              {/* 今日卡路里 - 炫酷重新设计 */}
              <div
                className="dashboard-card relative overflow-hidden bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-orange-200/50 shadow-xl"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200/20 to-red-200/20 rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-200/20 to-orange-200/20 rounded-full translate-y-12 -translate-x-12"></div>

                <div className="relative z-10">
                  {/* 标题区域 */}
                  <div className="text-center mb-6">
                    <div className="inline-flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                        <span className="text-xl">🔥</span>
                      </div>
                      <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                        今日卡路里
                      </h3>
                    </div>

                    {/* 进度指示器 */}
                    <div className="w-full bg-white/60 rounded-full h-3 mb-3 shadow-inner">
                      <div
                        className="progress-bar bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full shadow-sm transition-all duration-1000 ease-out"
                        style={{ width: `${Math.min((todaySummary.totalCalories / todaySummary.calorieLimit) * 100, 100)}%` }}
                      ></div>
                    </div>

                    <p className="text-sm text-slate-600">
                      还需 <span className="font-bold text-orange-600">{Math.round(Math.max(todaySummary.calorieLimit - todaySummary.totalCalories, 0))}</span> 卡路里达到目标
                    </p>
                  </div>

                  {/* 数据展示区域 */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-lg border border-white/50">
                      <div className="calorie-number text-2xl sm:text-3xl font-bold text-orange-600 mb-1">
                        {Math.round(todaySummary.totalCalories)}
                      </div>
                      <div className="text-xs text-slate-600 font-medium">已摄入</div>
                      <div className="text-xs text-orange-500">kcal</div>
                    </div>
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-lg border border-white/50">
                      <div className="calorie-number text-2xl sm:text-3xl font-bold text-red-600 mb-1">
                        {Math.round(todaySummary.calorieLimit)}
                      </div>
                      <div className="text-xs text-slate-600 font-medium">目标</div>
                      <div className="text-xs text-red-500">kcal</div>
                    </div>
                  </div>


                  {/* 营养计划卡片 - 第二位 */}
                  <div
                    className="dashboard-card bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-white/20 shadow-lg"
                    onMouseEnter={handleCardHover}
                    onMouseLeave={handleCardLeave}
                  >
                    <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-4 flex items-center gap-2">
                      <span className="text-xl">🍽️</span>
                      营养计划
                    </h3>
                    <div className="space-y-4 sm:space-y-6">
                      {/* 每日卡路里限额 */}
                      <div className="bg-gradient-to-r from-lime-50 to-green-50 rounded-xl p-4 border border-lime-200/50 shadow-lg">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-lime-500 to-green-500 rounded-lg flex items-center justify-center shadow-md">
                            <span className="text-sm">🔥</span>
                          </div>
                          <h4 className="text-lg font-bold bg-gradient-to-r from-lime-600 to-green-600 bg-clip-text text-transparent">
                            每日卡路里限额
                          </h4>
                        </div>
                        <div className="text-center">
                          <div className="text-3xl sm:text-4xl font-bold text-green-700 mb-2">
                            {Math.round(profile.dailyCalorieLimit)}
                          </div>
                          <div className="text-sm text-green-600 mb-3">千卡 / 天</div>
                          <div className="w-full bg-green-200/50 rounded-full h-2">
                            <div className="bg-gradient-to-r from-lime-400 to-green-500 h-2 rounded-full" style={{ width: '100%' }}></div>
                          </div>
                          <div className="text-xs text-green-500 mt-2">基于您的身体数据计算</div>
                        </div>
                      </div>

                      {/* 三餐分配 */}
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center shadow-md">
                            <span className="text-sm">🍴</span>
                          </div>
                          <h4 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                            三餐分配
                          </h4>
                        </div>
                        <div className="grid grid-cols-1 gap-3 sm:gap-4">
                          {/* 早餐 */}
                          <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-4 border border-amber-200/50 shadow-lg">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-amber-400 to-orange-400 rounded-lg flex items-center justify-center shadow-md">
                                  <span className="text-sm">🌅</span>
                                </div>
                                <div>
                                  <div className="font-bold text-amber-800 text-sm sm:text-base">早餐</div>
                                  <div className="text-xs text-amber-600">开启美好一天</div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-lg sm:text-xl font-bold text-amber-700">
                                  {Math.round(todaySummary.mealBreakdown.breakfast.calories)} / {Math.round(profile.dailyCalorieLimit * 0.3)} kcal
                                </div>
                                <div className="text-xs text-amber-600">
                                  {Math.round((todaySummary.mealBreakdown.breakfast.calories / (profile.dailyCalorieLimit * 0.3)) * 100)}%
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* 午餐 */}
                          <div className="bg-gradient-to-r from-sky-50 to-blue-50 rounded-xl p-4 border border-sky-200/50 shadow-lg">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-sky-400 to-blue-400 rounded-lg flex items-center justify-center shadow-md">
                                  <span className="text-sm">☀️</span>
                                </div>
                                <div>
                                  <div className="font-bold text-sky-800 text-sm sm:text-base">午餐</div>
                                  <div className="text-xs text-sky-600">补充能量</div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-lg sm:text-xl font-bold text-sky-700">
                                  {Math.round(todaySummary.mealBreakdown.lunch.calories)} / {Math.round(profile.dailyCalorieLimit * 0.4)} kcal
                                </div>
                                <div className="text-xs text-sky-600">
                                  {Math.round((todaySummary.mealBreakdown.lunch.calories / (profile.dailyCalorieLimit * 0.4)) * 100)}%
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* 晚餐 */}
                          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 border border-indigo-200/50 shadow-lg">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-lg flex items-center justify-center shadow-md">
                                  <span className="text-sm">🌙</span>
                                </div>
                                <div>
                                  <div className="font-bold text-indigo-800 text-sm sm:text-base">晚餐</div>
                                  <div className="text-xs text-indigo-600">营养收尾</div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-lg sm:text-xl font-bold text-indigo-700">
                                  {Math.round(todaySummary.mealBreakdown.dinner.calories)} / {Math.round(profile.dailyCalorieLimit * 0.3)} kcal
                                </div>
                                <div className="text-xs text-indigo-600">
                                  {Math.round((todaySummary.mealBreakdown.dinner.calories / (profile.dailyCalorieLimit * 0.3)) * 100)}%
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>



                  {/* 营养详情 - 炫酷数据可视化重新设计 */}
                  <div
                    className="dashboard-card relative overflow-hidden bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-emerald-200/50 shadow-xl"
                    onMouseEnter={handleCardHover}
                    onMouseLeave={handleCardLeave}
                  >
                    {/* 背景装饰 */}
                    <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-emerald-200/20 to-teal-200/20 rounded-full -translate-y-20 -translate-x-20"></div>
                    <div className="absolute bottom-0 right-0 w-28 h-28 bg-gradient-to-tl from-cyan-200/20 to-emerald-200/20 rounded-full translate-y-14 translate-x-14"></div>

                    <div className="relative z-10">
                      {/* 标题区域 */}
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg">
                          <span className="text-xl">📊</span>
                        </div>
                        <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                          营养详情
                        </h3>
                      </div>

                      {/* 营养素网格 */}
                      <div className="grid grid-cols-2 gap-3 sm:gap-4">
                        {/* 蛋白质 */}
                        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-slate-600">蛋白质</span>
                            <span className="text-xs text-emerald-600">🥩</span>
                          </div>
                          <div className="text-xl sm:text-2xl font-bold text-emerald-600 mb-1">
                            {Math.round(todaySummary.nutrition.protein)}g
                          </div>
                          <div className="w-full bg-emerald-100 rounded-full h-2">
                            <div className="bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.protein / 150) * 100, 100)}%` }}></div>
                          </div>
                        </div>

                        {/* 脂肪 */}
                        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-slate-600">脂肪</span>
                            <span className="text-xs text-amber-600">🥑</span>
                          </div>
                          <div className="text-xl sm:text-2xl font-bold text-amber-600 mb-1">
                            {Math.round(todaySummary.nutrition.fat)}g
                          </div>
                          <div className="w-full bg-amber-100 rounded-full h-2">
                            <div className="bg-gradient-to-r from-amber-400 to-amber-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.fat / 80) * 100, 100)}%` }}></div>
                          </div>
                        </div>

                        {/* 碳水化合物 */}
                        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-slate-600">碳水</span>
                            <span className="text-xs text-blue-600">🍞</span>
                          </div>
                          <div className="text-xl sm:text-2xl font-bold text-blue-600 mb-1">
                            {Math.round(todaySummary.nutrition.carbs)}g
                          </div>
                          <div className="w-full bg-blue-100 rounded-full h-2">
                            <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.carbs / 300) * 100, 100)}%` }}></div>
                          </div>
                        </div>

                        {/* 纤维 */}
                        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-slate-600">纤维</span>
                            <span className="text-xs text-green-600">🥬</span>
                          </div>
                          <div className="text-xl sm:text-2xl font-bold text-green-600 mb-1">
                            {Math.round(todaySummary.nutrition.fiber)}g
                          </div>
                          <div className="w-full bg-green-100 rounded-full h-2">
                            <div className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.fiber / 35) * 100, 100)}%` }}></div>
                          </div>
                        </div>
                      </div>

                      {/* 营养建议提示 */}
                      <div className="mt-4 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-white/50">
                        <div className="flex items-center gap-2 text-sm text-slate-600 mb-2">
                          <span className="text-emerald-500">💧</span>
                          <span>记得补充水分 - 建议每天饮水1.5-2升，有助于新陈代谢和减重</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-slate-600">
                          <span className="text-emerald-500">💡</span>
                          <span>营养均衡是健康的基础</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* 代谢信息 - 炫酷重新设计 */}
                  <div className="bg-gradient-to-r from-rose-50 to-pink-50 rounded-xl p-4 border border-rose-200/50 shadow-lg">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-gradient-to-r from-rose-500 to-pink-500 rounded-lg flex items-center justify-center shadow-md">
                        <span className="text-sm">⚡</span>
                      </div>
                      <h4 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent">
                        代谢信息
                      </h4>
                    </div>

                    <div className="space-y-3">
                      {/* BMR */}
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm text-slate-600 mb-1">基础代谢率 (BMR)</div>
                            <div className="text-xs text-slate-500">静息状态下的能量消耗</div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-rose-600">
                              {Math.round(profile.bmr)}
                            </div>
                            <div className="text-xs text-rose-500">kcal/天</div>
                          </div>
                        </div>
                        <div className="mt-3 w-full bg-rose-100 rounded-full h-2">
                          <div className="bg-gradient-to-r from-rose-400 to-rose-600 h-2 rounded-full" style={{ width: `${Math.min((profile.bmr / 2500) * 100, 100)}%` }}></div>
                        </div>
                      </div>

                      {/* TDEE */}
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm text-slate-600 mb-1">总日消耗 (TDEE)</div>
                            <div className="text-xs text-slate-500">包含活动的总消耗</div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-pink-600">
                              {Math.round(profile.tdee)}
                            </div>
                            <div className="text-xs text-pink-500">kcal/天</div>
                          </div>
                        </div>
                        <div className="mt-3 w-full bg-pink-100 rounded-full h-2">
                          <div className="bg-gradient-to-r from-pink-400 to-pink-600 h-2 rounded-full" style={{ width: `${Math.min((profile.tdee / 3500) * 100, 100)}%` }}></div>
                        </div>
                      </div>

                      {/* 代谢效率指示 */}
                      <div className="bg-gradient-to-r from-rose-100/50 to-pink-100/50 rounded-lg p-3 border border-rose-200/30">
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-rose-500">🔥</span>
                          <span className="text-slate-600">
                            代谢效率：<span className="font-semibold text-rose-600">
                              {Math.round((profile.tdee / profile.bmr - 1) * 100)}%
                            </span> 活动加成
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>


                  {/* 快速操作按钮 */}
                  <button
                    className="action-button btn btn-primary btn-lg w-full h-14 sm:h-16 shadow-lg"
                    onClick={(e) => { handleButtonClick(e); alert('食物记录功能即将推出'); }}
                  >
                    <span className="text-xl sm:text-2xl mr-2 sm:mr-3">🍽️</span>
                    <div className="text-left">
                      <div className="font-bold text-sm sm:text-base">开始记录食物</div>
                      <div className="text-xs opacity-80">记录今日饮食</div>
                    </div>
                  </button>

                  {/* 次要操作按钮网格 */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <button
                      className="action-button btn btn-secondary btn-lg h-12 sm:h-14 shadow-lg"
                      onClick={(e) => { handleButtonClick(e); addTestData(); }}
                    >
                      <span className="text-lg sm:text-xl mr-2">🧪</span>
                      <div className="text-left">
                        <div className="font-bold text-sm">添加测试数据</div>
                        <div className="text-xs opacity-80">快速体验</div>
                      </div>
                    </button>
                    <button
                      className="action-button btn btn-accent btn-lg h-12 sm:h-14 shadow-lg"
                      onClick={(e) => { handleButtonClick(e); navigate('/calendar'); }}
                    >
                      <span className="text-lg sm:text-xl mr-2">📅</span>
                      <div className="text-left">
                        <div className="font-bold text-sm">查看日历</div>
                        <div className="text-xs opacity-80">历史记录</div>
                      </div>
                    </button>
                  </div>

                  {/* 测试功能按钮 */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <button
                      className="action-button btn btn-outline btn-lg h-12 sm:h-14 shadow-lg"
                      onClick={(e) => { handleButtonClick(e); navigate('/camera-test'); }}
                    >
                      <span className="text-lg sm:text-xl mr-2">📷</span>
                      <div className="text-left">
                        <div className="font-bold text-sm">相机测试</div>
                        <div className="text-xs opacity-80">拍照功能</div>
                      </div>
                    </button>
                    <button
                      className="action-button btn btn-outline btn-lg h-12 sm:h-14 shadow-lg"
                      onClick={(e) => { handleButtonClick(e); navigate('/ai-test'); }}
                    >
                      <span className="text-lg sm:text-xl mr-2">🤖</span>
                      <div className="text-left">
                        <div className="font-bold text-sm">AI识别测试</div>
                        <div className="text-xs opacity-80">智能识别</div>
                      </div>
                    </button>
                  </div>


                  {/* 重置按钮 */}
                  <button
                    className="action-button btn btn-ghost btn-lg w-full h-12 sm:h-14 shadow-lg border-2 border-dashed border-base-300"
                    onClick={(e) => { handleButtonClick(e); clearProfile(); }}
                  >
                    <span className="text-lg sm:text-xl mr-2">🔄</span>
                    <div className="text-left">
                      <div className="font-bold text-sm">重新设置档案</div>
                      <div className="text-xs opacity-80">重新开始</div>
                    </div>
                  </button>

                </div>
              </div>
              {/* 底部留白，避免内容被底部导航栏遮挡 - 适配全面屏安全区域 */}
              <div className="h-24 pb-safe"></div>
            </div>
          )}
        </div>
      </div>

      {/* 底部固定导航栏 - 移到根级别避免transform层叠上下文影响 */}
      <BottomNavigation
        onRecordFood={handleRecordFood}
        onViewProfile={handleViewProfile}
      />
    </div>
  );
};

export default DashboardPage;
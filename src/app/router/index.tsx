import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import { PageTransition } from '@/shared/components/animations';
import ProfileSetupPage from '@/app/pages/ProfileSetup';
import DashboardPage from '@/app/pages/Dashboard';
import CalendarPage from '@/app/pages/CalendarPage';
import CameraTestPage from '@/app/pages/CameraTest';
import AITestPage from '@/app/pages/AITest';
import NavigationDebugPage from '@/app/pages/NavigationDebugPage';
import ProfilePage from '@/app/pages/ProfilePage';
import FoodRecordPage from '@/app/pages/FoodRecord';

// 路由守卫组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete } = useUserStore();
  
  if (!isProfileComplete) {
    return <Navigate to="/setup" replace />;
  }
  
  return <>{children}</>;
};

// 设置页面守卫
const SetupRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete } = useUserStore();
  
  if (isProfileComplete) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

// 根路由重定向
const RootRedirect: React.FC = () => {
  const { isProfileComplete } = useUserStore();
  
  return <Navigate to={isProfileComplete ? "/dashboard" : "/setup"} replace />;
};

export const router = createBrowserRouter([
  {
    path: "/",
    element: <RootRedirect />
  },
  {
    path: "/setup",
    element: (
      <SetupRoute>
        <PageTransition>
          <ProfileSetupPage />
        </PageTransition>
      </SetupRoute>
    )
  },
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <DashboardPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/calendar",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <CalendarPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/camera-test",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <CameraTestPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/ai-test",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <AITestPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/profile",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <ProfilePage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/food-record",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <FoodRecordPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/debug-nav",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <NavigationDebugPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "*",
    element: <Navigate to="/" replace />
  }
]);